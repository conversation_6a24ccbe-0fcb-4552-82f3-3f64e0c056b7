<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蜘蛛机器人运动步态模拟</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #45a049;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">加载中...</div>
        
        <div id="info">
            <h3>蜘蛛机器人步态模拟</h3>
            <p>点击地面来指定机器人的目标位置</p>
            <p>状态: <span id="status">初始化中</span></p>
            <p>位置: <span id="position">-</span></p>
        </div>
        
        <div id="controls">
            <button id="regenerateBtn">重新生成地形</button>
            <button id="resetBtn">重置机器人</button>
            <button id="pauseBtn">暂停/继续</button>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.9/dat.gui.min.js"></script>
    
    <script src="js/terrain.js"></script>
    <script src="js/spider.js"></script>
    <script src="js/pathfinding.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
