// 路径规划器类
class PathFinder {
    constructor() {
        this.gridSize = 0.5; // 网格大小
        this.maxSearchDistance = 50;
    }
    
    findPath(start, end, terrain) {
        // 将世界坐标转换为网格坐标
        const startGrid = this.worldToGrid(start);
        const endGrid = this.worldToGrid(end);
        
        // 使用A*算法寻路
        const path = this.aStar(startGrid, endGrid, terrain);
        
        if (!path || path.length === 0) {
            return null;
        }
        
        // 将网格路径转换回世界坐标
        const worldPath = path.map(gridPos => this.gridToWorld(gridPos, terrain));
        
        // 路径平滑化
        return this.smoothPath(worldPath, terrain);
    }
    
    worldToGrid(worldPos) {
        return {
            x: Math.round(worldPos.x / this.gridSize),
            y: Math.round(worldPos.y / this.gridSize),
            z: Math.round(worldPos.z / this.gridSize)
        };
    }
    
    gridToWorld(gridPos, terrain) {
        const worldX = gridPos.x * this.gridSize;
        const worldZ = gridPos.z * this.gridSize;
        const worldY = terrain.getGroundHeight(worldX, worldZ) + 1; // 稍微抬高
        
        return new THREE.Vector3(worldX, worldY, worldZ);
    }
    
    aStar(start, end, terrain) {
        const openSet = [];
        const closedSet = new Set();
        const cameFrom = new Map();
        const gScore = new Map();
        const fScore = new Map();
        
        const startKey = this.gridKey(start);
        const endKey = this.gridKey(end);
        
        openSet.push(start);
        gScore.set(startKey, 0);
        fScore.set(startKey, this.heuristic(start, end));
        
        while (openSet.length > 0) {
            // 找到f值最小的节点
            let current = openSet.reduce((min, node) => {
                const minF = fScore.get(this.gridKey(min)) || Infinity;
                const nodeF = fScore.get(this.gridKey(node)) || Infinity;
                return nodeF < minF ? node : min;
            });
            
            const currentKey = this.gridKey(current);
            
            // 到达目标
            if (currentKey === endKey) {
                return this.reconstructPath(cameFrom, current);
            }
            
            // 从开放集移除，加入关闭集
            openSet.splice(openSet.indexOf(current), 1);
            closedSet.add(currentKey);
            
            // 检查邻居
            const neighbors = this.getNeighbors(current, terrain);
            
            for (let neighbor of neighbors) {
                const neighborKey = this.gridKey(neighbor);
                
                if (closedSet.has(neighborKey)) {
                    continue;
                }
                
                const tentativeGScore = (gScore.get(currentKey) || Infinity) + 
                                      this.distance(current, neighbor);
                
                if (!openSet.some(node => this.gridKey(node) === neighborKey)) {
                    openSet.push(neighbor);
                } else if (tentativeGScore >= (gScore.get(neighborKey) || Infinity)) {
                    continue;
                }
                
                cameFrom.set(neighborKey, current);
                gScore.set(neighborKey, tentativeGScore);
                fScore.set(neighborKey, tentativeGScore + this.heuristic(neighbor, end));
            }
        }
        
        return null; // 没有找到路径
    }
    
    gridKey(gridPos) {
        return `${gridPos.x},${gridPos.y},${gridPos.z}`;
    }
    
    heuristic(a, b) {
        // 使用曼哈顿距离作为启发式函数
        return Math.abs(a.x - b.x) + Math.abs(a.y - b.y) + Math.abs(a.z - b.z);
    }
    
    distance(a, b) {
        // 欧几里得距离
        const dx = a.x - b.x;
        const dy = a.y - b.y;
        const dz = a.z - b.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    getNeighbors(gridPos, terrain) {
        const neighbors = [];
        const directions = [
            // 水平移动
            { x: 1, y: 0, z: 0 },
            { x: -1, y: 0, z: 0 },
            { x: 0, y: 0, z: 1 },
            { x: 0, y: 0, z: -1 },
            // 对角线移动
            { x: 1, y: 0, z: 1 },
            { x: 1, y: 0, z: -1 },
            { x: -1, y: 0, z: 1 },
            { x: -1, y: 0, z: -1 },
            // 垂直移动（爬坡/下坡）
            { x: 0, y: 1, z: 0 },
            { x: 0, y: -1, z: 0 }
        ];
        
        for (let dir of directions) {
            const neighbor = {
                x: gridPos.x + dir.x,
                y: gridPos.y + dir.y,
                z: gridPos.z + dir.z
            };
            
            if (this.isValidPosition(neighbor, terrain)) {
                neighbors.push(neighbor);
            }
        }
        
        return neighbors;
    }
    
    isValidPosition(gridPos, terrain) {
        const worldPos = this.gridToWorld(gridPos, terrain);
        
        // 检查是否在地形范围内
        if (Math.abs(worldPos.x) > terrain.terrainSize/2 || 
            Math.abs(worldPos.z) > terrain.terrainSize/2) {
            return false;
        }
        
        // 检查是否可以站立
        return terrain.canStandAt(worldPos.x, worldPos.y, worldPos.z, 0.8);
    }
    
    reconstructPath(cameFrom, current) {
        const path = [current];
        let currentKey = this.gridKey(current);
        
        while (cameFrom.has(currentKey)) {
            current = cameFrom.get(currentKey);
            path.unshift(current);
            currentKey = this.gridKey(current);
        }
        
        return path;
    }
    
    smoothPath(path, terrain) {
        if (path.length <= 2) {
            return path;
        }
        
        const smoothed = [path[0]];
        let current = 0;
        
        while (current < path.length - 1) {
            let farthest = current + 1;
            
            // 找到可以直接到达的最远点
            for (let i = current + 2; i < path.length; i++) {
                if (this.canMoveDirectly(path[current], path[i], terrain)) {
                    farthest = i;
                } else {
                    break;
                }
            }
            
            smoothed.push(path[farthest]);
            current = farthest;
        }
        
        return smoothed;
    }
    
    canMoveDirectly(start, end, terrain) {
        const direction = end.clone().sub(start);
        const distance = direction.length();
        direction.normalize();
        
        const steps = Math.ceil(distance / this.gridSize);
        
        for (let i = 1; i < steps; i++) {
            const testPos = start.clone().add(
                direction.clone().multiplyScalar(i * this.gridSize)
            );
            
            if (!terrain.canStandAt(testPos.x, testPos.y, testPos.z, 0.8)) {
                return false;
            }
        }
        
        return true;
    }
    
    // 可视化路径（调试用）
    visualizePath(path, scene) {
        // 清除之前的路径可视化
        if (this.pathVisualization) {
            scene.remove(this.pathVisualization);
        }
        
        if (!path || path.length < 2) {
            return;
        }
        
        const points = path.map(pos => new THREE.Vector3(pos.x, pos.y + 0.1, pos.z));
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({ 
            color: 0xff0000, 
            linewidth: 3 
        });
        
        this.pathVisualization = new THREE.Line(geometry, material);
        scene.add(this.pathVisualization);
        
        // 添加路径点标记
        const pointGeometry = new THREE.SphereGeometry(0.1, 8, 6);
        const pointMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
        
        path.forEach((point, index) => {
            if (index === 0 || index === path.length - 1) {
                const marker = new THREE.Mesh(pointGeometry, pointMaterial);
                marker.position.copy(point);
                marker.position.y += 0.2;
                this.pathVisualization.add(marker);
            }
        });
    }
    
    // 动态重新规划路径
    replanPath(currentPos, targetPos, terrain, avoidanceRadius = 1.0) {
        // 检查当前路径是否仍然有效
        if (this.currentPath && this.isPathValid(this.currentPath, terrain)) {
            return this.currentPath;
        }
        
        // 重新规划路径
        return this.findPath(currentPos, targetPos, terrain);
    }
    
    isPathValid(path, terrain) {
        if (!path || path.length < 2) {
            return false;
        }
        
        for (let i = 0; i < path.length; i++) {
            const point = path[i];
            if (!terrain.canStandAt(point.x, point.y, point.z, 0.8)) {
                return false;
            }
        }
        
        return true;
    }
    
    // 获取路径上的下一个目标点
    getNextTarget(currentPos, path, lookAheadDistance = 2.0) {
        if (!path || path.length === 0) {
            return null;
        }
        
        // 找到距离当前位置最近的路径点
        let closestIndex = 0;
        let closestDistance = Infinity;
        
        for (let i = 0; i < path.length; i++) {
            const distance = currentPos.distanceTo(path[i]);
            if (distance < closestDistance) {
                closestDistance = distance;
                closestIndex = i;
            }
        }
        
        // 向前查找目标点
        let targetIndex = closestIndex;
        let accumulatedDistance = 0;
        
        for (let i = closestIndex; i < path.length - 1; i++) {
            const segmentDistance = path[i].distanceTo(path[i + 1]);
            if (accumulatedDistance + segmentDistance >= lookAheadDistance) {
                targetIndex = i + 1;
                break;
            }
            accumulatedDistance += segmentDistance;
            targetIndex = i + 1;
        }
        
        return path[targetIndex];
    }
}
