// 主应用程序类
class SpiderBotDemo {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.terrain = null;
        this.spider = null;
        this.pathfinder = null;
        
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        this.isRunning = true;
        this.targetPosition = null;
        this.currentPath = [];
        
        this.init();
    }
    
    init() {
        this.setupScene();
        this.setupLighting();
        this.setupControls();
        this.setupEventListeners();
        
        // 初始化各个系统
        this.terrain = new TerrainGenerator(this.scene);
        this.spider = new SpiderRobot(this.scene);
        this.pathfinder = new PathFinder();
        
        // 生成初始地形
        this.terrain.generate();
        
        // 将蜘蛛放置在地形上的合适位置
        this.placeSpiderOnTerrain();
        
        // 开始渲染循环
        this.animate();
        
        // 隐藏加载界面
        document.getElementById('loading').style.display = 'none';
        this.updateStatus('就绪 - 点击地面来移动机器人');
    }
    
    setupScene() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // 天蓝色背景
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(20, 15, 20);
        this.camera.lookAt(0, 0, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        document.getElementById('container').appendChild(this.renderer.domElement);
        
        // 添加雾效
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // 主方向光（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 25);
        directionalLight.castShadow = true;
        
        // 配置阴影
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        
        this.scene.add(directionalLight);
        
        // 补充光源
        const fillLight = new THREE.DirectionalLight(0x8888ff, 0.3);
        fillLight.position.set(-25, 25, -25);
        this.scene.add(fillLight);
    }
    
    setupControls() {
        // 简单的轨道控制（鼠标控制相机）
        this.controls = {
            mouseDown: false,
            mouseX: 0,
            mouseY: 0,
            cameraDistance: 30,
            cameraAngleX: 0,
            cameraAngleY: 0.3
        };
    }
    
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 鼠标事件
        this.renderer.domElement.addEventListener('click', (event) => this.onMouseClick(event));
        this.renderer.domElement.addEventListener('mousedown', (event) => this.onMouseDown(event));
        this.renderer.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        this.renderer.domElement.addEventListener('mouseup', () => this.onMouseUp());
        this.renderer.domElement.addEventListener('wheel', (event) => this.onMouseWheel(event));
        
        // 按钮事件
        document.getElementById('regenerateBtn').addEventListener('click', () => this.regenerateTerrain());
        document.getElementById('resetBtn').addEventListener('click', () => this.resetSpider());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    onMouseClick(event) {
        if (this.controls.mouseDown) return; // 如果是拖拽，不处理点击
        
        // 计算鼠标位置
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        // 射线检测
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.terrain.meshes);
        
        if (intersects.length > 0) {
            const point = intersects[0].point;
            this.setTarget(point);
        }
    }
    
    onMouseDown(event) {
        this.controls.mouseDown = true;
        this.controls.mouseX = event.clientX;
        this.controls.mouseY = event.clientY;
    }
    
    onMouseMove(event) {
        if (!this.controls.mouseDown) return;
        
        const deltaX = event.clientX - this.controls.mouseX;
        const deltaY = event.clientY - this.controls.mouseY;
        
        this.controls.cameraAngleX -= deltaX * 0.01;
        this.controls.cameraAngleY -= deltaY * 0.01;
        
        // 限制垂直角度
        this.controls.cameraAngleY = Math.max(-Math.PI/2 + 0.1, Math.min(Math.PI/2 - 0.1, this.controls.cameraAngleY));
        
        this.controls.mouseX = event.clientX;
        this.controls.mouseY = event.clientY;
        
        this.updateCamera();
    }
    
    onMouseUp() {
        this.controls.mouseDown = false;
    }
    
    onMouseWheel(event) {
        this.controls.cameraDistance += event.deltaY * 0.01;
        this.controls.cameraDistance = Math.max(5, Math.min(100, this.controls.cameraDistance));
        this.updateCamera();
    }
    
    updateCamera() {
        const spiderPos = this.spider ? this.spider.position : new THREE.Vector3(0, 0, 0);
        
        this.camera.position.x = spiderPos.x + Math.cos(this.controls.cameraAngleX) * Math.cos(this.controls.cameraAngleY) * this.controls.cameraDistance;
        this.camera.position.y = spiderPos.y + Math.sin(this.controls.cameraAngleY) * this.controls.cameraDistance;
        this.camera.position.z = spiderPos.z + Math.sin(this.controls.cameraAngleX) * Math.cos(this.controls.cameraAngleY) * this.controls.cameraDistance;
        
        this.camera.lookAt(spiderPos);
    }
    
    setTarget(position) {
        this.targetPosition = position.clone();

        // 添加目标位置标记
        this.addTargetMarker(position);

        // 寻路
        const path = this.pathfinder.findPath(
            this.spider.position,
            this.targetPosition,
            this.terrain
        );

        if (path && path.length > 0) {
            this.currentPath = path;
            this.spider.setPath(path);

            // 可视化路径
            this.pathfinder.visualizePath(path, this.scene);

            this.updateStatus('移动中...');
        } else {
            this.updateStatus('无法到达目标位置');
        }
    }

    addTargetMarker(position) {
        // 移除之前的标记
        if (this.targetMarker) {
            this.scene.remove(this.targetMarker);
        }

        // 创建新的目标标记
        const markerGeometry = new THREE.ConeGeometry(0.2, 1, 8);
        const markerMaterial = new THREE.MeshLambertMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.8
        });

        this.targetMarker = new THREE.Mesh(markerGeometry, markerMaterial);
        this.targetMarker.position.copy(position);
        this.targetMarker.position.y += 0.5;

        // 添加发光效果
        const glowGeometry = new THREE.RingGeometry(0.5, 1.0, 16);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide
        });

        const glow = new THREE.Mesh(glowGeometry, glowMaterial);
        glow.rotation.x = -Math.PI / 2;
        glow.position.y = 0.1;

        this.targetMarker.add(glow);
        this.scene.add(this.targetMarker);

        // 添加动画效果
        this.animateTargetMarker();
    }

    animateTargetMarker() {
        if (this.targetMarker) {
            const time = Date.now() * 0.005;
            this.targetMarker.rotation.y = time;
            this.targetMarker.children[0].material.opacity = 0.2 + 0.2 * Math.sin(time * 2);
        }
    }
    
    placeSpiderOnTerrain() {
        // 找到一个合适的位置放置蜘蛛
        const position = this.terrain.findSuitableSpawnPosition();
        this.spider.setPosition(position);
    }
    
    regenerateTerrain() {
        this.terrain.generate();
        this.placeSpiderOnTerrain();
        this.updateStatus('地形已重新生成');
    }
    
    resetSpider() {
        this.placeSpiderOnTerrain();
        this.spider.reset();
        this.currentPath = [];
        this.targetPosition = null;
        this.updateStatus('机器人已重置');
    }
    
    togglePause() {
        this.isRunning = !this.isRunning;
        document.getElementById('pauseBtn').textContent = this.isRunning ? '暂停' : '继续';
        this.updateStatus(this.isRunning ? '运行中' : '已暂停');
    }
    
    updateStatus(status) {
        document.getElementById('status').textContent = status;
    }
    
    updatePosition() {
        if (this.spider) {
            const pos = this.spider.position;
            document.getElementById('position').textContent = 
                `(${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)})`;
        }
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());

        if (this.isRunning) {
            // 更新蜘蛛
            if (this.spider) {
                this.spider.update(this.terrain);
            }

            // 更新相机跟随
            this.updateCamera();

            // 更新UI
            this.updatePosition();

            // 更新目标标记动画
            this.animateTargetMarker();
        }

        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }
}

// 启动应用
let app;
window.addEventListener('load', () => {
    app = new SpiderBotDemo();
});
