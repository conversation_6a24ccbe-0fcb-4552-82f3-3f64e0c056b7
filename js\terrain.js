// 地形生成器类
class TerrainGenerator {
    constructor(scene) {
        this.scene = scene;
        this.meshes = [];
        this.blocks = new Map(); // 存储方块数据，key为"x,y,z"格式
        this.blockSize = 1;
        this.terrainSize = 30; // 地形大小 (30x30)
        this.maxHeight = 8;
        
        // 材质
        this.blockMaterial = new THREE.MeshLambertMaterial({
            color: 0x8B4513,
            transparent: false
        });

        // 创建纹理变化
        this.createBlockVariations();
        
        // 几何体缓存
        this.blockGeometry = new THREE.BoxGeometry(this.blockSize, this.blockSize, this.blockSize);
    }
    
    generate() {
        this.clear();
        this.generateHeightMap();
        this.createBlockMeshes();
        this.optimizeMeshes();
    }
    
    clear() {
        // 清除现有的网格
        this.meshes.forEach(mesh => {
            this.scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) mesh.material.dispose();
        });
        this.meshes = [];
        this.blocks.clear();
    }
    
    generateHeightMap() {
        // 使用简单的噪声函数生成高度图
        for (let x = -this.terrainSize/2; x < this.terrainSize/2; x++) {
            for (let z = -this.terrainSize/2; z < this.terrainSize/2; z++) {
                const height = this.getHeightAt(x, z);
                
                // 从底部到高度创建方块
                for (let y = 0; y <= height; y++) {
                    this.setBlock(x, y, z, true);
                }
            }
        }
    }
    
    getHeightAt(x, z) {
        // 简单的多层噪声函数
        let height = 0;
        
        // 基础地形
        height += Math.sin(x * 0.1) * Math.cos(z * 0.1) * 2;
        
        // 添加细节
        height += Math.sin(x * 0.3) * Math.cos(z * 0.3) * 1;
        height += Math.sin(x * 0.5) * Math.cos(z * 0.5) * 0.5;
        
        // 随机变化
        height += (Math.random() - 0.5) * 1;
        
        // 确保高度在合理范围内
        height = Math.max(1, Math.min(this.maxHeight, Math.floor(height + 3)));
        
        return height;
    }
    
    setBlock(x, y, z, exists) {
        const key = `${x},${y},${z}`;
        if (exists) {
            this.blocks.set(key, { x, y, z, exists: true });
        } else {
            this.blocks.delete(key);
        }
    }
    
    getBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        return this.blocks.get(key) || { x, y, z, exists: false };
    }
    
    hasBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        return this.blocks.has(key);
    }
    
    createBlockMeshes() {
        // 为了性能，我们将相邻的方块合并成更大的几何体
        const instancedMesh = new THREE.InstancedMesh(
            this.blockGeometry,
            this.blockMaterial,
            this.blocks.size
        );
        
        let index = 0;
        const matrix = new THREE.Matrix4();
        
        this.blocks.forEach((block) => {
            // 只渲染暴露的面（优化性能）
            if (this.isBlockExposed(block.x, block.y, block.z)) {
                matrix.setPosition(block.x, block.y, block.z);
                instancedMesh.setMatrixAt(index, matrix);
                index++;
            }
        });
        
        instancedMesh.instanceMatrix.needsUpdate = true;
        instancedMesh.castShadow = true;
        instancedMesh.receiveShadow = true;
        
        this.scene.add(instancedMesh);
        this.meshes.push(instancedMesh);
    }
    
    isBlockExposed(x, y, z) {
        // 检查六个方向是否有相邻方块
        const directions = [
            [1, 0, 0], [-1, 0, 0],
            [0, 1, 0], [0, -1, 0],
            [0, 0, 1], [0, 0, -1]
        ];
        
        for (let [dx, dy, dz] of directions) {
            if (!this.hasBlock(x + dx, y + dy, z + dz)) {
                return true; // 至少有一个面暴露
            }
        }
        return false;
    }
    
    optimizeMeshes() {
        // 创建更详细的碰撞检测网格
        this.createCollisionMeshes();
    }
    
    createCollisionMeshes() {
        // 为每个暴露的方块创建单独的碰撞网格
        this.blocks.forEach((block) => {
            if (this.isBlockExposed(block.x, block.y, block.z)) {
                const mesh = new THREE.Mesh(
                    this.blockGeometry,
                    new THREE.MeshBasicMaterial({ 
                        visible: false, // 不可见，仅用于碰撞检测
                        transparent: true,
                        opacity: 0
                    })
                );
                mesh.position.set(block.x, block.y, block.z);
                mesh.userData = { 
                    isBlock: true, 
                    blockPos: { x: block.x, y: block.y, z: block.z }
                };
                
                this.scene.add(mesh);
                this.meshes.push(mesh);
            }
        });
    }
    
    // 获取指定位置的地面高度
    getGroundHeight(x, z) {
        let maxHeight = -Infinity;
        
        // 检查周围的方块
        for (let dx = -0.5; dx <= 0.5; dx += 0.5) {
            for (let dz = -0.5; dz <= 0.5; dz += 0.5) {
                const blockX = Math.floor(x + dx);
                const blockZ = Math.floor(z + dz);
                
                // 找到这个位置最高的方块
                for (let y = this.maxHeight; y >= 0; y--) {
                    if (this.hasBlock(blockX, y, blockZ)) {
                        maxHeight = Math.max(maxHeight, y + 0.5);
                        break;
                    }
                }
            }
        }
        
        return maxHeight === -Infinity ? 0 : maxHeight;
    }
    
    // 检查指定位置是否可以站立
    canStandAt(x, y, z, radius = 0.5) {
        // 检查脚下是否有支撑
        const groundHeight = this.getGroundHeight(x, z);
        if (Math.abs(y - groundHeight) > 0.1) {
            return false;
        }
        
        // 检查周围是否有足够空间
        const checkRadius = Math.ceil(radius);
        for (let dx = -checkRadius; dx <= checkRadius; dx++) {
            for (let dz = -checkRadius; dz <= checkRadius; dz++) {
                if (dx * dx + dz * dz <= radius * radius) {
                    const blockX = Math.floor(x + dx);
                    const blockZ = Math.floor(z + dz);
                    const blockY = Math.floor(y + 1); // 检查头部空间
                    
                    if (this.hasBlock(blockX, blockY, blockZ)) {
                        return false;
                    }
                }
            }
        }
        
        return true;
    }
    
    // 找到合适的生成位置
    findSuitableSpawnPosition() {
        const attempts = 100;
        
        for (let i = 0; i < attempts; i++) {
            const x = (Math.random() - 0.5) * this.terrainSize * 0.8;
            const z = (Math.random() - 0.5) * this.terrainSize * 0.8;
            const y = this.getGroundHeight(x, z);
            
            if (this.canStandAt(x, y, z, 1)) {
                return new THREE.Vector3(x, y + 1, z);
            }
        }
        
        // 如果找不到合适位置，返回中心位置
        const centerY = this.getGroundHeight(0, 0);
        return new THREE.Vector3(0, centerY + 1, 0);
    }
    
    // 射线检测，用于寻路和地面检测
    raycast(origin, direction, maxDistance = 10) {
        const raycaster = new THREE.Raycaster(origin, direction, 0, maxDistance);
        const intersects = raycaster.intersectObjects(this.meshes.filter(mesh => mesh.userData.isBlock));

        return intersects.length > 0 ? intersects[0] : null;
    }

    // 多方向射线检测，寻找最佳着陆点
    findSurfaceContact(position, radius = 0.2) {
        const results = [];

        // 向下射线检测（顶面）
        const downOrigin = position.clone().add(new THREE.Vector3(0, 2, 0));
        const downDirection = new THREE.Vector3(0, -1, 0);
        const downHit = this.raycast(downOrigin, downDirection, 5);

        if (downHit) {
            results.push({
                position: downHit.point,
                normal: downHit.face.normal,
                distance: downHit.distance,
                surface: 'top',
                blockPos: downHit.object.userData.blockPos
            });
        }

        // 侧面射线检测
        const sideDirections = [
            new THREE.Vector3(1, 0, 0),   // 东
            new THREE.Vector3(-1, 0, 0),  // 西
            new THREE.Vector3(0, 0, 1),   // 北
            new THREE.Vector3(0, 0, -1),  // 南
        ];

        sideDirections.forEach((direction, index) => {
            const origin = position.clone().sub(direction.clone().multiplyScalar(2));
            const hit = this.raycast(origin, direction, 4);

            if (hit) {
                results.push({
                    position: hit.point,
                    normal: hit.face.normal,
                    distance: hit.distance,
                    surface: ['east', 'west', 'north', 'south'][index],
                    blockPos: hit.object.userData.blockPos
                });
            }
        });

        // 按距离排序
        results.sort((a, b) => a.distance - b.distance);
        return results;
    }
    
    // 获取表面法向量
    getSurfaceNormal(x, y, z, face = 'top') {
        switch (face) {
            case 'top': return new THREE.Vector3(0, 1, 0);
            case 'bottom': return new THREE.Vector3(0, -1, 0);
            case 'north': return new THREE.Vector3(0, 0, 1);
            case 'south': return new THREE.Vector3(0, 0, -1);
            case 'east': return new THREE.Vector3(1, 0, 0);
            case 'west': return new THREE.Vector3(-1, 0, 0);
            default: return new THREE.Vector3(0, 1, 0);
        }
    }

    // 创建方块材质变化
    createBlockVariations() {
        const colors = [
            0x8B4513, // 棕色
            0x654321, // 深棕色
            0x9B5513, // 浅棕色
            0x7B4513, // 中棕色
        ];

        this.blockMaterials = colors.map(color =>
            new THREE.MeshLambertMaterial({
                color: color,
                transparent: false
            })
        );
    }

    // 获取随机材质
    getRandomMaterial() {
        if (!this.blockMaterials) return this.blockMaterial;
        return this.blockMaterials[Math.floor(Math.random() * this.blockMaterials.length)];
    }
}
