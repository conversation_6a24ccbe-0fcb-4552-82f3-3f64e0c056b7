// 蜘蛛机器人类
class SpiderRobot {
    constructor(scene) {
        this.scene = scene;
        this.group = new THREE.Group();
        this.scene.add(this.group);
        
        // 基本属性
        this.position = new THREE.Vector3(0, 0, 0);
        this.rotation = 0; // Y轴旋转
        this.bodyRadius = 0.8;
        this.legLength = 1.5;
        
        // 运动相关
        this.speed = 2.0;
        this.currentPath = [];
        this.pathIndex = 0;
        this.isMoving = false;
        
        // 步态相关
        this.stepHeight = 0.5;
        this.stepDuration = 0.3;
        this.gaitPhase = 0;
        
        // 腿部数据
        this.legs = [];
        this.legTargets = [];
        this.legStates = []; // 'ground', 'lifting', 'moving', 'placing'
        
        this.createBody();
        this.createLegs();
        this.initializeGait();
    }
    
    createBody() {
        // 创建球形身体
        const bodyGeometry = new THREE.SphereGeometry(this.bodyRadius, 16, 12);
        const bodyMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x333333,
            transparent: false
        });
        
        this.body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        this.body.castShadow = true;
        this.body.receiveShadow = true;
        this.group.add(this.body);
        
        // 添加一些细节
        const eyeGeometry = new THREE.SphereGeometry(0.1, 8, 6);
        const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
        
        // 创建眼睛
        for (let i = 0; i < 4; i++) {
            const eye = new THREE.Mesh(eyeGeometry, eyeMaterial);
            const angle = (i / 4) * Math.PI * 2;
            eye.position.set(
                Math.cos(angle) * this.bodyRadius * 0.8,
                0.2,
                Math.sin(angle) * this.bodyRadius * 0.8
            );
            this.body.add(eye);
        }
    }
    
    createLegs() {
        const legPositions = [
            // 右侧腿部 (从前到后)
            { angle: -Math.PI/6, side: 1 },      // 右前腿
            { angle: -Math.PI/2, side: 1 },      // 右中腿  
            { angle: -5*Math.PI/6, side: 1 },    // 右后腿
            // 左侧腿部 (从前到后)
            { angle: Math.PI/6, side: -1 },      // 左前腿
            { angle: Math.PI/2, side: -1 },      // 左中腿
            { angle: 5*Math.PI/6, side: -1 }     // 左后腿
        ];
        
        legPositions.forEach((legPos, index) => {
            const leg = this.createSingleLeg(legPos.angle, legPos.side, index);
            this.legs.push(leg);
            this.group.add(leg.group);
        });
    }
    
    createSingleLeg(angle, side, index) {
        const legGroup = new THREE.Group();
        
        // 腿部连接点（髋关节）
        const hipPosition = new THREE.Vector3(
            Math.cos(angle) * this.bodyRadius,
            0,
            Math.sin(angle) * this.bodyRadius
        );
        
        // 髋关节
        const hipGeometry = new THREE.SphereGeometry(0.15, 8, 6);
        const hipMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const hip = new THREE.Mesh(hipGeometry, hipMaterial);
        hip.position.copy(hipPosition);
        legGroup.add(hip);
        
        // 大腿
        const thighGeometry = new THREE.CylinderGeometry(0.08, 0.08, this.legLength * 0.6);
        const thighMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const thigh = new THREE.Mesh(thighGeometry, thighMaterial);
        thigh.castShadow = true;
        
        // 膝关节
        const kneeGeometry = new THREE.SphereGeometry(0.12, 8, 6);
        const kneeMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const knee = new THREE.Mesh(kneeGeometry, kneeMaterial);
        
        // 小腿
        const shinGeometry = new THREE.CylinderGeometry(0.06, 0.06, this.legLength * 0.6);
        const shinMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
        const shin = new THREE.Mesh(shinGeometry, shinMaterial);
        shin.castShadow = true;
        
        // 脚部
        const footGeometry = new THREE.SphereGeometry(0.1, 8, 6);
        const footMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
        const foot = new THREE.Mesh(footGeometry, footMaterial);
        foot.castShadow = true;
        
        // 组装腿部
        const thighGroup = new THREE.Group();
        thighGroup.add(thigh);
        thighGroup.add(knee);
        
        const shinGroup = new THREE.Group();
        shinGroup.add(shin);
        shinGroup.add(foot);
        
        thighGroup.add(shinGroup);
        legGroup.add(thighGroup);
        
        // 设置初始位置
        thighGroup.position.copy(hipPosition);
        knee.position.y = -this.legLength * 0.3;
        shinGroup.position.y = -this.legLength * 0.3;
        shin.position.y = -this.legLength * 0.3;
        foot.position.y = -this.legLength * 0.3;
        
        // 计算初始脚部位置
        const footWorldPos = new THREE.Vector3(
            hipPosition.x + Math.cos(angle) * this.legLength * 0.8,
            -this.legLength,
            hipPosition.z + Math.sin(angle) * this.legLength * 0.8
        );
        
        const leg = {
            group: legGroup,
            hip: hip,
            thighGroup: thighGroup,
            knee: knee,
            shinGroup: shinGroup,
            shin: shin,
            foot: foot,
            index: index,
            angle: angle,
            side: side,
            hipPosition: hipPosition,
            currentFootPos: footWorldPos.clone(),
            targetFootPos: footWorldPos.clone(),
            restFootPos: footWorldPos.clone(),
            isMoving: false,
            moveProgress: 0
        };
        
        this.legTargets.push(footWorldPos.clone());
        this.legStates.push('ground');
        
        return leg;
    }
    
    initializeGait() {
        // 初始化三角步态
        // 腿部分组：[0,2,4] 和 [1,3,5] 交替移动
        this.gaitGroups = [
            [0, 2, 4], // 右前、右后、左中
            [1, 3, 5]  // 右中、左前、左后
        ];
        this.currentGaitGroup = 0;
        this.gaitTimer = 0;
    }
    
    setPosition(position) {
        this.position.copy(position);
        this.group.position.copy(position);
        this.updateLegPositions();
    }
    
    setPath(path) {
        this.currentPath = path.slice();
        this.pathIndex = 0;
        this.isMoving = true;
    }
    
    reset() {
        this.currentPath = [];
        this.pathIndex = 0;
        this.isMoving = false;
        this.gaitPhase = 0;
        this.gaitTimer = 0;
        
        // 重置腿部状态
        this.legStates.fill('ground');
        this.legs.forEach(leg => {
            leg.isMoving = false;
            leg.moveProgress = 0;
        });
    }
    
    update(terrain) {
        if (this.isMoving && this.currentPath.length > 0) {
            this.updateMovement(terrain);
        }
        
        this.updateGait(terrain);
        this.updateLegIK();
    }
    
    updateMovement(terrain) {
        if (this.pathIndex >= this.currentPath.length) {
            this.isMoving = false;
            return;
        }

        const target = this.currentPath[this.pathIndex];
        const direction = target.clone().sub(this.position);
        const distance = direction.length();

        if (distance < 0.5) {
            // 到达当前路径点，移动到下一个
            this.pathIndex++;
            return;
        }

        // 平滑移动身体
        direction.normalize();
        const moveDistance = this.speed * 0.016; // 假设60fps
        this.position.add(direction.multiplyScalar(moveDistance));

        // 平滑旋转
        const targetRotation = Math.atan2(direction.x, direction.z);
        const rotationDiff = targetRotation - this.rotation;

        // 处理角度环绕
        let adjustedDiff = rotationDiff;
        if (adjustedDiff > Math.PI) adjustedDiff -= 2 * Math.PI;
        if (adjustedDiff < -Math.PI) adjustedDiff += 2 * Math.PI;

        this.rotation += adjustedDiff * 0.1; // 平滑插值

        // 添加身体摆动效果
        const swayAmount = 0.05;
        const swaySpeed = 5.0;
        const bodyTilt = Math.sin(Date.now() * 0.001 * swaySpeed) * swayAmount;

        // 更新身体位置和旋转
        this.group.position.copy(this.position);
        this.group.rotation.y = this.rotation;
        this.group.rotation.z = bodyTilt;

        // 添加轻微的上下摆动
        const bobAmount = 0.02;
        const bobSpeed = 8.0;
        this.group.position.y += Math.sin(Date.now() * 0.001 * bobSpeed) * bobAmount;
    }
    
    updateGait(terrain) {
        this.gaitTimer += 0.016; // 假设60fps

        // 检查是否需要移动腿部
        if (this.isMoving) {
            const gaitCycleTime = 0.8; // 步态周期时间
            const phaseProgress = (this.gaitTimer % gaitCycleTime) / gaitCycleTime;

            // 三角步态：两组腿交替移动
            // 第一组：右前(0)、左中(4)、右后(2)
            // 第二组：左前(3)、右中(1)、左后(5)
            if (phaseProgress < 0.5) {
                this.updateLegGroup(0, phaseProgress * 2, terrain);
            } else {
                this.updateLegGroup(1, (phaseProgress - 0.5) * 2, terrain);
            }
        } else {
            // 静止时，确保所有腿都接触地面
            this.legs.forEach(leg => {
                if (!leg.isMoving) {
                    this.adjustLegToGround(leg, terrain);
                }
            });
        }
    }
    
    updateLegGroup(groupIndex, progress, terrain) {
        const legIndices = this.gaitGroups[groupIndex];
        
        legIndices.forEach(legIndex => {
            const leg = this.legs[legIndex];
            
            if (progress < 0.1 && !leg.isMoving) {
                // 开始移动这条腿
                this.startLegMovement(leg, terrain);
            }
            
            if (leg.isMoving) {
                this.updateLegMovement(leg, terrain);
            }
        });
    }
    
    startLegMovement(leg, terrain) {
        leg.isMoving = true;
        leg.moveProgress = 0;

        // 计算新的目标位置
        const bodyPos = this.position;
        const legDirection = new THREE.Vector3(
            Math.cos(leg.angle + this.rotation),
            0,
            Math.sin(leg.angle + this.rotation)
        );

        // 计算理想的脚部位置
        const idealDistance = this.legLength * 0.9;
        const targetPos = bodyPos.clone().add(
            legDirection.multiplyScalar(idealDistance)
        );

        // 使用更精确的地面检测
        const groundInfo = this.findBestFoothold(targetPos, terrain);
        if (groundInfo) {
            leg.targetFootPos.copy(groundInfo.position);
            leg.targetSurface = groundInfo.surface;
        } else {
            // 回退到简单的高度检测
            targetPos.y = terrain.getGroundHeight(targetPos.x, targetPos.z);
            leg.targetFootPos.copy(targetPos);
            leg.targetSurface = 'top';
        }
    }
    
    updateLegMovement(leg, terrain) {
        leg.moveProgress += 0.016 / this.stepDuration;
        
        if (leg.moveProgress >= 1.0) {
            // 完成移动
            leg.isMoving = false;
            leg.moveProgress = 0;
            leg.currentFootPos.copy(leg.targetFootPos);
            return;
        }
        
        // 插值计算当前脚部位置
        const startPos = leg.currentFootPos.clone();
        const endPos = leg.targetFootPos.clone();
        
        // 添加抬腿效果
        const liftHeight = Math.sin(leg.moveProgress * Math.PI) * this.stepHeight;
        
        leg.currentFootPos.lerpVectors(startPos, endPos, leg.moveProgress);
        leg.currentFootPos.y += liftHeight;
    }
    
    updateLegIK() {
        this.legs.forEach(leg => {
            this.solveLegIK(leg);
        });
    }
    
    solveLegIK(leg) {
        // 简化的逆运动学求解
        const hipWorldPos = this.position.clone().add(leg.hipPosition);
        const targetPos = leg.currentFootPos.clone();
        
        // 计算从髋关节到目标的向量
        const toTarget = targetPos.clone().sub(hipWorldPos);
        const distance = toTarget.length();
        
        // 限制距离
        const maxReach = this.legLength * 1.2;
        if (distance > maxReach) {
            toTarget.normalize().multiplyScalar(maxReach);
            targetPos.copy(hipWorldPos).add(toTarget);
        }
        
        // 计算关节角度
        const thighLength = this.legLength * 0.6;
        const shinLength = this.legLength * 0.6;
        
        // 膝关节角度（使用余弦定理）
        const kneeAngle = Math.acos(
            Math.max(-1, Math.min(1, 
                (thighLength * thighLength + shinLength * shinLength - distance * distance) /
                (2 * thighLength * shinLength)
            ))
        );
        
        // 髋关节角度
        const hipAngle = Math.atan2(toTarget.y, Math.sqrt(toTarget.x * toTarget.x + toTarget.z * toTarget.z)) +
                        Math.acos(Math.max(-1, Math.min(1,
                            (thighLength * thighLength + distance * distance - shinLength * shinLength) /
                            (2 * thighLength * distance)
                        )));
        
        // 应用旋转
        leg.thighGroup.rotation.x = -hipAngle;
        leg.thighGroup.rotation.y = Math.atan2(toTarget.x, toTarget.z);
        leg.shinGroup.rotation.x = kneeAngle - Math.PI;
    }
    
    updateLegPositions() {
        // 更新所有腿部的目标位置
        this.legs.forEach((leg, index) => {
            const worldHipPos = this.position.clone().add(leg.hipPosition);
            const legDirection = new THREE.Vector3(
                Math.cos(leg.angle + this.rotation),
                0,
                Math.sin(leg.angle + this.rotation)
            );

            leg.restFootPos.copy(worldHipPos).add(
                legDirection.multiplyScalar(this.legLength * 0.8)
            );

            if (!leg.isMoving) {
                leg.currentFootPos.copy(leg.restFootPos);
                leg.targetFootPos.copy(leg.restFootPos);
            }
        });
    }

    // 寻找最佳的脚部着陆点（包括侧面）
    findBestFoothold(idealPos, terrain) {
        // 使用地形的表面接触检测
        const surfaceContacts = terrain.findSurfaceContact(idealPos, 0.2);

        if (surfaceContacts.length === 0) {
            // 回退到简单的顶面检测
            const groundHeight = terrain.getGroundHeight(idealPos.x, idealPos.z);
            if (groundHeight > -Infinity) {
                return {
                    position: new THREE.Vector3(idealPos.x, groundHeight, idealPos.z),
                    surface: 'top',
                    normal: new THREE.Vector3(0, 1, 0)
                };
            }
            return null;
        }

        // 选择最适合的表面
        for (let contact of surfaceContacts) {
            // 检查表面是否适合站立
            if (this.isSurfaceSuitable(contact, idealPos)) {
                return {
                    position: contact.position,
                    surface: contact.surface,
                    normal: contact.normal,
                    blockPos: contact.blockPos
                };
            }
        }

        return null;
    }

    // 检查表面是否适合脚部着陆
    isSurfaceSuitable(contact, idealPos) {
        // 检查距离是否合理
        const distance = contact.position.distanceTo(idealPos);
        if (distance > this.legLength * 0.5) {
            return false;
        }

        // 检查表面角度
        const upVector = new THREE.Vector3(0, 1, 0);
        const angle = contact.normal.angleTo(upVector);

        // 允许一定角度的倾斜表面
        if (angle > Math.PI / 3) { // 60度
            return false;
        }

        return true;
    }

    // 调整腿部到地面
    adjustLegToGround(leg, terrain) {
        const groundHeight = terrain.getGroundHeight(leg.currentFootPos.x, leg.currentFootPos.z);
        const targetY = groundHeight;

        // 平滑调整到地面
        const currentY = leg.currentFootPos.y;
        const diff = targetY - currentY;

        if (Math.abs(diff) > 0.05) {
            leg.currentFootPos.y += diff * 0.1; // 平滑插值
        }
    }
}
