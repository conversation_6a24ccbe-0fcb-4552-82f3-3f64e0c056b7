# 蜘蛛机器人运动步态模拟Demo

这是一个纯前端的3D蜘蛛机器人运动步态模拟展示项目，使用Three.js实现。

## 功能特性

### 1. 随机地形生成
- 类似Minecraft的方块体素地形
- 使用多层噪声函数生成自然的地形变化
- 支持不同高度的方块结构
- 优化的渲染性能，只渲染暴露的方块面

### 2. 六足蜘蛛机器人
- 球形身体设计，带有红色眼睛细节
- 6条腿，每条腿有髋关节、膝关节两个关节点
- 每条腿包含大腿和小腿两个部分
- 逆运动学(IK)系统控制腿部运动

### 3. 智能路径规划
- 基于A*算法的寻路系统
- 支持3D地形中的路径规划
- 路径平滑化处理
- 实时路径可视化

### 4. 自然步态控制
- 三角步态实现，确保机器人稳定行走
- 两组腿交替移动：
  - 第一组：右前腿、左中腿、右后腿
  - 第二组：左前腿、右中腿、左后腿
- 平滑的腿部抬起和放下动画
- 自适应地形的脚部着陆

### 5. 精确地面接触检测
- 多方向射线检测系统
- 支持在方块顶面和侧面行走
- 智能寻找最佳脚部着陆点
- 表面法向量计算，支持倾斜表面

### 6. 交互式用户界面
- 鼠标点击指定目标位置
- 实时状态显示和位置信息
- 相机跟随和手动控制
- 目标位置可视化标记

### 7. 视觉效果优化
- 实时阴影渲染
- 平滑的身体摆动和上下摆动效果
- 目标标记动画效果
- 路径可视化
- 多种方块材质变化

## 技术实现

### 核心技术栈
- **Three.js**: 3D渲染引擎
- **JavaScript ES6+**: 主要编程语言
- **HTML5 Canvas**: 渲染目标
- **CSS3**: 用户界面样式

### 主要算法
1. **地形生成**: 多层Perlin噪声算法
2. **路径规划**: A*寻路算法
3. **步态控制**: 三角步态算法
4. **逆运动学**: 简化的2关节IK求解
5. **碰撞检测**: 射线投射检测

### 文件结构
```
spiderbotdemo/
├── index.html          # 主HTML文件
├── js/
│   ├── main.js         # 主应用程序和场景管理
│   ├── terrain.js      # 地形生成和管理
│   ├── spider.js       # 蜘蛛机器人模型和控制
│   └── pathfinding.js  # 路径规划算法
└── README.md           # 项目说明文档
```

## 使用方法

### 启动项目
1. 在项目目录下启动HTTP服务器：
   ```bash
   python -m http.server 8000
   ```
   或使用其他HTTP服务器

2. 在浏览器中访问：
   ```
   http://localhost:8000
   ```

### 操作说明
- **移动机器人**: 点击地形上的任意位置，机器人会自动寻路并移动到目标位置
- **相机控制**: 
  - 鼠标拖拽：旋转视角
  - 滚轮：缩放距离
- **控制按钮**:
  - "重新生成地形"：生成新的随机地形
  - "重置机器人"：将机器人重置到初始位置
  - "暂停/继续"：暂停或继续模拟

### 观察要点
1. **步态协调**: 观察机器人的腿部如何协调运动，保持稳定
2. **地形适应**: 机器人如何在不同高度的方块上行走
3. **路径规划**: 红色线条显示规划的路径
4. **表面接触**: 腿部如何准确踩在方块表面上

## 性能优化

- 使用实例化渲染减少绘制调用
- 只渲染暴露的方块面
- 优化的阴影映射设置
- 平滑的动画插值减少计算量

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

需要支持WebGL的现代浏览器。

## 扩展可能性

1. **更复杂的地形**: 添加不同类型的方块、斜坡等
2. **多种步态**: 实现波浪步态、对角步态等
3. **物理模拟**: 添加重力、碰撞等物理效果
4. **多机器人**: 支持多个机器人同时运行
5. **任务系统**: 添加目标收集、路径优化等任务

## 开发说明

这个项目展示了以下计算机图形学和机器人学概念：
- 3D渲染和着色
- 程序化内容生成
- 路径规划算法
- 逆运动学
- 步态生成
- 实时动画系统

适合用于教学演示、技术展示或作为更复杂机器人模拟项目的基础。
